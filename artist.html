<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/artist.css">
    <!-- Flickity CSS CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flickity@2/dist/flickity.min.css">
    <title><PERSON> | Artist Page | Banshee Music</title>
</head>


<body>

      <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>

            <!-- Hamburger <PERSON><PERSON> -->
            <button type="button" class="hamburger" id="hamburger" aria-label="Toggle navigation menu" aria-expanded="false">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <ul class="menu" id="menu">
                <li><a href="index.html" aria-current="page">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
    </header>

    <a href="#main-content" class="skip-link">Skip to main content</a>

    <div id="aria-live-region" aria-live="polite">Notifications will appear here.</div>





    
    <main id="main-content" role="main" class="container">
        <div class="section artist-header">
            <div class="artist-header-backdrop"></div>
            <div class="artist-photo-container">
                <img src="imgs/album-04-B.png" alt="John Doe" class="artist-photo">
                <div class="artist-photo-overlay"></div>
            </div>
            <div class="artist-info">
                <div class="artist-name">
                    <h1>John Doe <span class="verified-badge" title="Verified Artist"><i class="fas fa-check-circle"></i></span></h1>
                    <div class="subtitle">Professional Music Artist</div>
                </div>
                <div class="artist-bio-container">
                    <p class="artist-bio" id="artist-bio">
                        Artist Biography goes here. This is a short description about the artist,
                        their journey, and their musical style. Lorem ipsum dolor sit amet,
                        consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        <br><br>
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                        <br><br>
                        The artist has released multiple albums and singles, collaborated with various renowned artists, and performed at major music festivals around the world. Their unique sound combines elements of electronic, pop, and indie music, creating a distinctive style that resonates with fans globally.
                    </p>
                    <button type="button" class="read-more-btn" aria-expanded="false" aria-controls="artist-bio">Read More</button>
                </div>
                <div class="artist-stats">
                    <div class="stat">
                        <i class="fas fa-headphones-alt stat-icon"></i>
                        <span class="stat-number">2.5M</span>
                        <span class="stat-label">Monthly Listeners</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-users stat-icon"></i>
                        <span class="stat-number">500K</span>
                        <span class="stat-label">Followers</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-compact-disc stat-icon"></i>
                        <span class="stat-number">15</span>
                        <span class="stat-label">Albums</span>
                    </div>
                </div>
                <div class="artist-actions">
                    <button type="button" class="action-btn play-btn" aria-label="Play artist's top track">
                        <i class="fas fa-play"></i>
                        Play
                    </button>
                    <button type="button" class="action-btn follow-btn" aria-label="Follow artist">
                        <i class="fas fa-user-plus"></i>
                        <span class="follow-text">Follow</span>
                    </button>
                    <button type="button" class="action-btn share-btn" aria-label="Share artist">
                        <i class="fas fa-share-alt"></i>
                        Share
                    </button>
                    <!-- Share Modal -->
                    <div id="shareModal" class="share-modal" hidden aria-modal="true" role="dialog" aria-labelledby="shareModalTitle">
                        <div class="share-modal-content">
                            <h3 id="shareModalTitle">Share Artist</h3>
                            <div class="share-options">
                                <button type="button" class="share-option" data-platform="facebook" aria-label="Share on Facebook">
                                    <i class="fab fa-facebook-f"></i> Facebook
                                </button>
                                <button type="button" class="share-option" data-platform="twitter" aria-label="Share on Twitter">
                                    <i class="fab fa-x-twitter"></i> Twitter
                                </button>
                                <button type="button" class="share-option" data-platform="instagram" aria-label="Share on Instagram">
                                    <i class="fab fa-instagram"></i> Instagram
                                </button>
                                <button type="button" class="share-option copy-link" aria-label="Copy share link">
                                    <i class="fas fa-link"></i> Copy Link
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



         <div class="content-wrapper">

                <div class="section-divider small">
                    <div class="divider-line"></div>
                    <div class="divider-icon"><i class="fas fa-headphones"></i></div>
                    <div class="divider-line"></div>
                </div>

                <div class="section demos">
                    <h2>Demo Tracks</h2>
                    <div class="track-carousel" data-flickity='{"cellAlign": "left", "contain": true, "pageDots": false, "wrapAround": true, "autoPlay": false, "prevNextButtons": true, "adaptiveHeight": false, "draggable": true, "freeScroll": false, "groupCells": 1, "imagesLoaded": true}'>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Midnight Dreams, 3:45, Electronic, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-tech-house-vibes-130.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-03-B.png" alt="Track 1 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Midnight Dreams</h3>
                                <p class="track-info">3:45 • Electronic • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">3:45</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Ocean Waves, 4:12, Ambient, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-dreaming-big-31.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-04-B.png" alt="Track 2 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Ocean Waves</h3>
                                <p class="track-info">4:12 • Ambient • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">4:12</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Neon Lights, 3:28, Synthwave, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-hip-hop-02-614.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-01-B.png" alt="Track 3 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Neon Lights</h3>
                                <p class="track-info">3:28 • Synthwave • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">3:28</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Urban Jungle, 5:02, Downtempo, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-dreaming-big-31.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-04-B.png" alt="Track 4 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Urban Jungle</h3>
                                <p class="track-info">5:02 • Downtempo • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">5:02</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Cosmic Journey, 6:15, Space Ambient, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-hip-hop-02-614.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-01-B.png" alt="Track 5 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Cosmic Journey</h3>
                                <p class="track-info">6:15 • Space Ambient • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">6:15</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Digital Dreams, 4:30, Electronica, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-tech-house-vibes-130.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-03-B.png" alt="Track 6 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Digital Dreams</h3>
                                <p class="track-info">4:30 • Electronica • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">4:30</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Neon City, 3:55, Cyberpunk, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-dreaming-big-31.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-04-B.png" alt="Track 7 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Neon City</h3>
                                <p class="track-info">3:55 • Cyberpunk • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">3:55</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Midnight Drive, 4:45, Synthwave, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-hip-hop-02-614.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-02-B.png" alt="Track 8 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Midnight Drive</h3>
                                <p class="track-info">4:45 • Synthwave • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">4:45</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Stellar Voyage, 5:18, Ambient, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-dreaming-big-31.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-03-B.png" alt="Track 9 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Stellar Voyage</h3>
                                <p class="track-info">5:18 • Ambient • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">5:18</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Quantum Leap, 4:22, Electronic, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-tech-house-vibes-130.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-04-B.png" alt="Track 10 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Quantum Leap</h3>
                                <p class="track-info">4:22 • Electronic • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">4:22</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Cyber Dawn, 3:56, Cyberpunk, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-hip-hop-02-614.mp3">
                            <div class="track-cover">
                                <img src="imgs/album-01-B.png" alt="Track 11 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Cyber Dawn</h3>
                                <p class="track-info">3:56 • Cyberpunk • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">3:56</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="track-item" tabindex="0" role="group" aria-label="Play Retro Future, 4:10, Synthwave, 2023" data-preview="https://assets.mixkit.co/music/preview/mixkit-dreaming-big-31.mp4">
                            <div class="track-cover">
                                <img src="imgs/album-02-B.png" alt="Track 12 Cover" loading="lazy">
                                <div class="track-overlay"></div>
                            </div>
                            <div class="track-details">
                                <h3>Retro Future</h3>
                                <p class="track-info">4:10 • Synthwave • 2023</p>
                                <div class="track-progress"><div class="track-progress-bar"></div></div>
                                <div class="track-times">
                                    <span class="current-time">0:00</span> / <span class="total-time">4:10</span>
                                </div>
                                <div class="track-controls">
                                    <button type="button" class="track-play-btn" aria-label="Play track"><i class="fas fa-play"></i></button>
                                    <button type="button" class="track-mute-btn" aria-label="Mute track"><i class="fas fa-volume-up"></i></button>
                                    <span class="track-loading" aria-live="polite" hidden><i class="fas fa-spinner fa-spin"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Flickity handles carousel controls -->
                    <div class="carousel-options">
                        <button type="button" class="carousel-option-btn auto-play-btn" aria-label="Toggle auto-play">
                            <i class="fas fa-play-circle"></i>
                            <span>Auto-play</span>
                        </button>
                        <button type="button" class="carousel-option-btn view-all-btn" aria-label="View all tracks">
                            <i class="fas fa-th-large"></i>
                            <span>View All</span>
                        </button>
                    </div>
                </div>

                <div class="section-divider">
                    <div class="divider-line"></div>
                    <div class="divider-icon"><i class="fas fa-compact-disc"></i></div>
                    <div class="divider-line"></div>
                </div>

                <div class="section featured-albums">
                    <h2>Featured Albums</h2>
                    <div class="album-carousel" data-flickity='{"cellAlign": "left", "contain": true, "pageDots": true, "wrapAround": true, "autoPlay": 7000, "prevNextButtons": true, "adaptiveHeight": false, "draggable": true, "freeScroll": false, "groupCells": 1, "selectedAttraction": 0.2, "friction": 0.8, "imagesLoaded": true}'>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-01-B.png" alt="Midnight Dreams Album Cover">
                            <div class="album-info">
                                <div class="album-title">Midnight Dreams</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-02-B.png" alt="Ocean Waves Album Cover">
                            <div class="album-info">
                                <div class="album-title">Ocean Waves</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-03-B.png" alt="Neon Lights Album Cover">
                            <div class="album-info">
                                <div class="album-title">Neon Lights</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-04-B.png" alt="Cosmic Journey Album Cover">
                            <div class="album-info">
                                <div class="album-title">Cosmic Journey</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-01-B.png" alt="Digital Dreams Album Cover">
                            <div class="album-info">
                                <div class="album-title">Digital Dreams</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-02-B.png" alt="Neon City Album Cover">
                            <div class="album-info">
                                <div class="album-title">Neon City</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-03-B.png" alt="Midnight Drive Album Cover">
                            <div class="album-info">
                                <div class="album-title">Midnight Drive</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                        <div class="album-card">
                            <img class="album-artwork" src="imgs/album-04-B.png" alt="Quantum Leap Album Cover">
                            <div class="album-info">
                                <div class="album-title">Quantum Leap</div>
                                <div class="album-year">2023</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-divider">
                    <div class="divider-line"></div>
                    <div class="divider-icon"><i class="fas fa-images"></i></div>
                    <div class="divider-line"></div>
                </div>
                <div class="section artist-gallery">
                    <h2>Gallery</h2>
                    <div class="gallery-grid">
                        <div class="gallery-item" tabindex="0" role="button" aria-label="Open Gallery Image 1"><img src="imgs/album-01-B.png" alt="Gallery Image 1" class="gallery-img"></div>
                        <div class="gallery-item" tabindex="0" role="button" aria-label="Open Gallery Image 2"><img src="imgs/album-02-B.png" alt="Gallery Image 2" class="gallery-img"></div>
                        <div class="gallery-item" tabindex="0" role="button" aria-label="Open Gallery Image 3"><img src="imgs/album-03-B.png" alt="Gallery Image 3" class="gallery-img"></div>
                        <div class="gallery-item" tabindex="0" role="button" aria-label="Open Gallery Image 4"><img src="imgs/album-04-B.png" alt="Gallery Image 4" class="gallery-img"></div>
                        <div class="gallery-item" tabindex="0" role="button" aria-label="Open Gallery Video"><video src="https://www.w3schools.com/html/mov_bbb.mp4" class="gallery-video" controls poster="imgs/album-01-B.png" data-caption="Live at Summer Fest 2024"></video></div>
                        <div class="gallery-item" tabindex="0" role="button" aria-label="Open Gallery Image 5"><img src="imgs/album-05.png" alt="Gallery Image 5" class="gallery-img"></div>
                    </div>
                    <!-- Gallery Modal -->
                    <div id="galleryModal" class="gallery-modal" hidden aria-modal="true" role="dialog" aria-labelledby="galleryModalTitle">
                        <div class="gallery-modal-content">
                            <span class="close-gallery-modal" tabindex="0" aria-label="Close gallery">&times;</span>
                            <div id="galleryModalMedia"></div>
                        </div>
                    </div>
                </div>

                <div class="section-divider">
                    <div class="divider-line"></div>
                    <div class="divider-icon"><i class="fas fa-calendar-alt"></i></div>
                    <div class="divider-line"></div>
                </div>
                <div class="section artist-events">
                    <h2>Concerts & Special Events</h2>
                    <div class="events-grid">
                        <div class="event-card upcoming">
                            <div class="event-date">
                                <span class="event-day">12</span>
                                <span class="event-month">Jul</span>
                            </div>
                            <div class="event-info">
                                <div class="event-title">Summer Music Fest</div>
                                <div class="event-location"><i class="fas fa-map-marker-alt"></i> Central Park, NYC</div>
                                <div class="event-description">Join John Doe live with special guests for an unforgettable night of music and lights!</div>
                                <a href="#" class="event-ticket" target="_blank" rel="noopener noreferrer">Get Tickets</a>
                            </div>
                        </div>
                        <div class="event-card upcoming">
                            <div class="event-date">
                                <span class="event-day">28</span>
                                <span class="event-month">Aug</span>
                            </div>
                            <div class="event-info">
                                <div class="event-title">Neon Nights Tour</div>
                                <div class="event-location"><i class="fas fa-map-marker-alt"></i> The O2 Arena, London</div>
                                <div class="event-description">Experience the new album live with immersive visuals and sound.</div>
                                <a href="#" class="event-ticket" target="_blank" rel="noopener noreferrer">Get Tickets</a>
                            </div>
                        </div>
                        <div class="event-card past">
                            <div class="event-date">
                                <span class="event-day">15</span>
                                <span class="event-month">May</span>
                            </div>
                            <div class="event-info">
                                <div class="event-title">Spring Jam</div>
                                <div class="event-location"><i class="fas fa-map-marker-alt"></i> Tokyo Dome, Tokyo</div>
                                <div class="event-description">A sold-out show with electrifying performances and fan favorites.</div>
                                <span class="event-ticket past-label">Past Event</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Album Details Modal Placeholder -->
                <div id="albumDetailsModal" class="album-modal" hidden aria-modal="true" role="dialog" aria-labelledby="albumDetailsTitle">
                    <!-- Content will be populated by JavaScript -->
                </div>

                <!-- NOTE: For dynamic data, replace static content with API-driven rendering in the future. -->
            </div>
        </div>







    </main>




    
    <!-- Flickity JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/flickity@2/dist/flickity.pkgd.min.js"></script>
    <script>
document.addEventListener('DOMContentLoaded', function () {
    var carousel = document.querySelector('.track-carousel');
    if (carousel && !carousel.classList.contains('flickity-enabled')) {
        new Flickity(carousel, {
            cellAlign: 'left',
            contain: true,
            pageDots: false,
            wrapAround: true,
            autoPlay: false,
            prevNextButtons: true,
            adaptiveHeight: false,
            draggable: true,
            freeScroll: false,
            groupCells: 1,
            imagesLoaded: true
        });
    }
});
</script>
    <!-- Shared utilities -->
    <script src="js/utils.js"></script>
    <!-- Artist page JS -->
    <script src="js/artist.js"></script>

    <!-- Mini Player - Standardized Implementation -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>
            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>
            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>
            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</body>
</html>