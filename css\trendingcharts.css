:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);

    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;

    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);

    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);

    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);

    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: var(--background-primary);
    color: var(--text-primary);
}

/* Navbar Styles */
header {
    background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */
.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative;
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease-out;
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1);
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px;
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px;
    background: var(--header-gradient-start);
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px;
    background: transparent;
}

.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    z-index: 0;
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1;
    border-radius: inherit;
}

.dropdown a:hover {
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1;
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Skip link for accessibility */
.skip-link {
    position: absolute;
    left: -999px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: var(--neon-blue);
    color: #fff;
    z-index: 3000;
    padding: 0.5em 1em;
    border-radius: 8px;
    font-weight: 700;
    transition: left 0.2s;
}

.skip-link:focus {
    left: 10px;
    top: 10px;
    width: auto;
    height: auto;
    outline: 2px solid var(--cosmic-pink);
}

/* Main container styles */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 120px 20px 40px;
    min-height: 100vh;
}

/* Trending charts page layout */
.trending-charts-page {
    width: 100%;
}

/* Page header */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.charts-title-gradient {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1rem;
    letter-spacing: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    position: relative;
}

.charts-title-gradient::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 2px;
    box-shadow: 0 0 20px rgba(0, 224, 255, 0.5);
}

.page-description {
    color: var(--text-secondary);
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Chart Navigation */
.chart-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    gap: 2rem;
}

.chart-tabs {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-tab {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 600;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.chart-tab:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.08);
}

.chart-tab.active {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    color: white;
    box-shadow: 0 4px 15px rgba(0, 224, 255, 0.3);
}

.chart-tab i {
    font-size: 1rem;
}

.chart-filters {
    display: flex;
    gap: 1rem;
}

.chart-filter {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all var(--transition-normal);
    min-width: 140px;
}

.chart-filter:focus {
    outline: none;
    border-color: var(--neon-blue);
    box-shadow: 0 0 0 3px rgba(0, 224, 255, 0.1);
}

.chart-filter option {
    background: var(--background-primary);
    color: var(--text-primary);
}

/* Featured Chart Hero */
.featured-chart-hero {
    position: relative;
    height: 400px;
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
    padding: 3rem;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: blur(20px) brightness(0.3);
    transform: scale(1.1);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 224, 255, 0.3) 0%,
        rgba(255, 0, 110, 0.3) 50%,
        rgba(111, 0, 255, 0.3) 100%
    );
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.hero-badge i {
    color: #ffd700;
}

.hero-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

.hero-artist {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
}

.stat i {
    color: var(--neon-blue);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-play-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hero-play-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 224, 255, 0.4), 0 12px 35px rgba(255, 0, 110, 0.4);
}

.hero-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.hero-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Charts Grid */
.charts-grid {
    display: grid;
    gap: 3rem;
    margin-bottom: 3rem;
}

/* Chart Sections */
.chart-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 2rem;
    transition: all var(--transition-normal);
}

.chart-section:hover {
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chart-header h2 i {
    color: var(--neon-blue);
    font-size: 1.5rem;
}

.view-all-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-secondary);
    padding: 0.75rem 1.25rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.view-all-btn:hover {
    color: var(--text-primary);
    border-color: var(--neon-blue);
    background: rgba(0, 224, 255, 0.1);
    transform: translateY(-2px);
}

/* Chart Lists */
.chart-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chart-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1.25rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.chart-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-position {
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--neon-blue);
    min-width: 40px;
    text-align: center;
}

.chart-position.top-3 {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.chart-item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.chart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chart-item-info {
    flex: 1;
    min-width: 0;
}

.chart-item-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chart-item-artist {
    color: var(--text-secondary);
    font-size: 0.95rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chart-item-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    min-width: 100px;
}

.chart-item-plays {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.chart-item-change {
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.chart-item-change.up {
    color: var(--cyber-lime);
}

.chart-item-change.down {
    color: var(--error-color);
}

.chart-item-change.new {
    color: var(--neon-blue);
}

.chart-item-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.chart-item:hover .chart-item-actions {
    opacity: 1;
}

.chart-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-secondary);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.chart-action-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.chart-action-btn.play:hover {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    color: white;
}

/* Chart Grids (for artists/albums) */
.chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    text-align: center;
    position: relative;
}

.chart-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.chart-card-position {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.chart-card-image {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    margin: 0 auto 1rem;
    position: relative;
}

.chart-card-image.artist {
    border-radius: 50%;
}

.chart-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chart-card-image .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.chart-card:hover .play-overlay {
    opacity: 1;
}

.chart-card-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.chart-card-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.chart-card-stats {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
}

/* Chart Statistics Section */
.chart-stats-section {
    background: linear-gradient(135deg, rgba(0, 224, 255, 0.05), rgba(255, 0, 110, 0.05));
    border: 1px solid rgba(0, 224, 255, 0.2);
    border-radius: 16px;
    padding: 2.5rem;
    margin-bottom: 2rem;
}

.chart-stats-section h2 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: var(--neon-blue);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3);
}

.stat-number {
    color: var(--text-primary);
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.loading-spinner {
    margin-bottom: 2rem;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid var(--neon-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-state p {
    font-size: 1.2rem;
    color: var(--text-primary);
    font-weight: 500;
}

/* --- Mini Player: Modern, Spacious, and Styled --- */
.mini-player {
    position: fixed;
    left: 50%;
    bottom: 32px;
    transform: translateX(-50%);
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    color: #fff;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(56, 12, 97, 0.25);
    display: flex;
    align-items: center;
    gap: 2.2rem;
    padding: 1.2rem 2.8rem 1.2rem 2rem;
    z-index: 2000;
    min-width: 440px;
    max-width: 650px;
    width: auto;
    animation: fadeInUp 0.4s;
}
.mini-player[hidden], .mini-player.hide {
    display: none !important;
}
.mini-player-info {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    min-width: 0;
    max-width: 260px;
    flex: 1 1 220px;
}
.mini-player-cover {
    flex-shrink: 0;
    width: 64px;
    height: 64px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.22);
    margin-right: 1rem;
}
.mini-player-meta {
    min-width: 100px;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-title {
    font-weight: 700;
    font-size: 1.13rem;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0,0,0,0.18);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-artist {
    font-size: 1rem;
    color: #e0e0e0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-playpause {
    background: rgba(0,0,0,0.22);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    font-size: 1.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    outline: none;
    border: 2px solid transparent;
}
.mini-player-playpause:focus {
    border: 2px solid var(--neon-blue);
    background: rgba(0,224,255,0.18);
}
.mini-player-playpause:hover {
    background: rgba(255,255,255,0.22);
    color: var(--cosmic-pink);
}
.mini-player-playpause i {
    pointer-events: none;
    margin: 0 auto;
}
.mini-player-progress {
    min-width: 120px;
    max-width: 180px;
    accent-color: var(--cosmic-pink);
    background: transparent;
    height: 5px;
    border-radius: 2px;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
}
.mini-player-time {
    font-size: 1rem;
    color: #fff;
    min-width: 70px;
    max-width: 90px;
    text-align: right;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
}
.mini-player-volume-label {
    font-size: 1.1rem;
    color: #fff;
    margin-left: 0.7rem;
    margin-right: 0.2rem;
    vertical-align: middle;
}
.mini-player-volume {
    min-width: 70px;
    max-width: 100px;
    accent-color: var(--neon-blue);
    vertical-align: middle;
    margin-left: 0.2rem;
    margin-right: 0.7rem;
}
.mini-player audio {
    display: none;
}
.mini-player-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 2.1rem;
    margin-left: 1rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}
.mini-player-close:hover, .mini-player-close:focus {
    opacity: 1;
    outline: none;
}
@media (max-width: 900px) {
    .mini-player {
        min-width: 0;
        max-width: 98vw;
        padding: 0.7rem 0.5rem;
        gap: 1.2rem;
    }
    .mini-player-info {
        max-width: 160px;
        gap: 0.7rem;
    }
    .mini-player-cover {
        width: 44px;
        height: 44px;
        margin-right: 0.5rem;
    }
    .mini-player-meta {
        max-width: 90px;
    }
    .mini-player-progress {
        min-width: 60px;
        max-width: 100%;
    }
}
@media (max-width: 600px) {
    .mini-player {
        flex-direction: column;
        align-items: stretch;
        gap: 0.7rem;
        min-width: 0;
        max-width: 99vw;
        padding: 0.7rem 0.3rem;
    }
    .mini-player-info {
        flex-direction: row;
        gap: 0.7rem;
        max-width: 100vw;
    }
    .mini-player-meta {
        max-width: 80vw;
    }
    .mini-player-progress {
        width: 100%;
        min-width: 0;
        max-width: 100%;
        margin: 0.5rem 0;
    }
    .mini-player-time {
        margin: 0.2rem 0;
    }
}

/* Mini Player Accessibility & Animation Enhancements */
.mini-player {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: #222;
  color: #fff;
  padding: 20px;
  border-radius: 10px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.4);
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 95vw;
  transition: box-shadow 0.3s;
}
.mini-player:focus-within {
  box-shadow: 0 0 0 3px var(--cosmic-pink, #FF006E);
}
.mini-player-row {
  display: flex;
  align-items: center;
  gap: 10px;
}
.mini-player-info {
  margin-top: 8px;
  font-size: 1rem;
  line-height: 1.2;
}
.mini-progress-row {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.mini-control-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  outline: none;
}
.mini-control-btn:focus {
  background: var(--hover-color, #333);
  box-shadow: 0 0 0 2px var(--cosmic-pink, #FF006E);
}
#progressBar {
  flex: 1 1 0%;
  accent-color: var(--cosmic-pink, #FF006E);
  height: 4px;
  border-radius: 2px;
  background: linear-gradient(90deg, var(--neon-blue, #00E0FF), var(--cosmic-pink, #FF006E));
  transition: background 0.3s;
  outline: none;
}
#progressBar:focus {
  box-shadow: 0 0 0 2px var(--cosmic-pink, #FF006E);
}
#progressBar::-webkit-slider-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--cosmic-pink, #FF006E);
  box-shadow: 0 0 8px var(--cosmic-pink, #FF006E);
  transition: box-shadow 0.2s;
  animation: pulse-thumb 1.2s infinite alternate;
}
#progressBar::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--cosmic-pink, #FF006E);
  box-shadow: 0 0 8px var(--cosmic-pink, #FF006E);
  transition: box-shadow 0.2s;
  animation: pulse-thumb 1.2s infinite alternate;
}
#progressBar::-ms-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--cosmic-pink, #FF006E);
  box-shadow: 0 0 8px var(--cosmic-pink, #FF006E);
  transition: box-shadow 0.2s;
  animation: pulse-thumb 1.2s infinite alternate;
}
@keyframes pulse-thumb {
  0% { box-shadow: 0 0 8px var(--cosmic-pink, #FF006E); }
  100% { box-shadow: 0 0 16px var(--cosmic-pink, #FF006E), 0 0 32px var(--neon-blue, #00E0FF, 0.3); }
}
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0,0,0,0) !important;
  border: 0 !important;
}
@media (max-width: 600px) {
  .mini-player {
    min-width: 0;
    width: 98vw;
    left: 1vw;
    right: 1vw;
    padding: 10px;
    font-size: 0.95rem;
  }
  .mini-player-info {
    max-width: 160px;
    gap: 0.7rem;
  }
  .mini-player-cover {
    width: 44px;
    height: 44px;
    margin-right: 0.5rem;
  }
  .mini-player-meta {
    max-width: 90px;
  }
  .mini-player-progress {
    min-width: 60px;
    max-width: 100%;
  }
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.05); /* Softer, more transparent */
  box-shadow: 0 0 8px 2px #00e0ff22; /* Softer glow */
  border: 1.5px solid rgba(0,224,255,0.13); /* More transparent border */
  padding: 2px 12px; /* Slightly more padding for a modern look */
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e55, 0 0 8px 2px #00e0ff55; /* Softer neon glow */
  border: 1.5px solid rgba(255, 0, 111, 0); /* Softer border */
  background: rgba(255,0,110,0.08); /* Softer background */
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding:  2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* Mini Player Styles */
#mini-player {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1.5rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.4);
    padding: 0.75rem 1.5rem 0.75rem 0.75rem;
    min-width: 260px;
    max-width: 95vw;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player[hidden] { display: none !important; }
#mini-player .mini-player-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
#mini-player-cover {
    width: 48px;
    height: 48px;
    border-radius: 0.75rem;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-right: 0.5rem;
}
#mini-player .mini-player-meta {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    min-width: 100px;
}
#mini-player-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-artist {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}
#mini-player-audio {
    display: none;
}
#mini-player-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
#mini-player-close:hover {
    color: var(--cosmic-pink);
}
#mini-player-playpause {
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    margin-left: 0.5rem;
    transition: box-shadow 0.2s, background 0.2s;
}
#mini-player-playpause:hover {
    box-shadow: var(--shadow-button-hover);
    background: var(--cosmic-pink);
}
.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 10px;
  display: none;
}
.mini-player-artwork.active {
  display: block;
}
@media (max-width: 600px) {
    #mini-player {
        min-width: 0;
        padding: 0.5rem 0.5rem 0.5rem 0.5rem;
        gap: 0.5rem;
    }
    #mini-player-title, #mini-player-artist {
        max-width: 90px;
    }
    #mini-player-cover {
        width: 36px;
        height: 36px;
    }
}

/* Simple Mini Player Styles */
#miniPlayerTest {
  position: fixed;
  bottom: 24px;
  left: 24px;
  background: #23242a;
  color: #fff;
  padding: 18px 22px 16px 22px;
  border-radius: 14px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25), 0 1.5px 8px rgba(111,0,255,0.08);
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 1rem;
  transition: box-shadow 0.2s;
}
#miniPlayerTest .mini-row {
  display: flex;
  align-items: center;
  gap: 14px;
}
#miniPlayerTest button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 7px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 0 8px 0 #6f00ff33;
}
#miniPlayerTest button:hover, #miniPlayerTest button:focus {
  background: #2d2e36;
  color: #00e0ff;
  outline: none;
  box-shadow: 0 0 16px 2px #00e0ff99, 0 0 8px 2px #ff006e99;
}
#miniPlayerTest .mini-info strong {
  font-weight: 600;
  color: #fff;
  border-radius: 6px;
  padding: 2px 8px;
  background: rgba(0,224,255,0.08);
  box-shadow: 0 0 8px 2px #00e0ff44;
  border: 1.5px solid #00e0ff33;
  transition: box-shadow 0.2s, border 0.2s;
}
#miniPlayerTest .mini-info strong.playing {
  box-shadow: 0 0 16px 4px #ff006e99, 0 0 8px 2px #00e0ff99;
  border: 1.5px solid #ff006e;
  background: rgba(255,0,110,0.10);
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* --- Mini Player: Modern, Spacious, and Styled --- */
.mini-player {
    position: fixed;
    left: 50%;
    bottom: 32px;
    transform: translateX(-50%);
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    color: #fff;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(56, 12, 97, 0.25);
    display: flex;
    align-items: center;
    gap: 2.2rem;
    padding: 1.2rem 2.8rem 1.2rem 2rem;
    z-index: 2000;
    min-width: 440px;
    max-width: 650px;
    width: auto;
    animation: fadeInUp 0.4s;
}
.mini-player[hidden], .mini-player.hide {
    display: none !important;
}
.mini-player-info {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    min-width: 0;
    max-width: 260px;
    flex: 1 1 220px;
}
.mini-player-cover {
    flex-shrink: 0;
    width: 64px;
    height: 64px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.22);
    margin-right: 1rem;
}
.mini-player-meta {
    min-width: 100px;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-title {
    font-weight: 700;
    font-size: 1.13rem;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0,0,0,0.18);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-artist {
    font-size: 1rem;
    color: #e0e0e0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-playpause {
    background: rgba(0,0,0,0.22);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    font-size: 1.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    outline: none;
    border: 2px solid transparent;
}
.mini-player-playpause:focus {
    border: 2px solid var(--neon-blue);
    background: rgba(0,224,255,0.18);
}
.mini-player-playpause:hover {
    background: rgba(255,255,255,0.22);
    color: var(--cosmic-pink);
}
.mini-player-playpause i {
    pointer-events: none;
    margin: 0 auto;
}
.mini-player-progress {
    min-width: 120px;
    max-width: 180px;
    accent-color: var(--cosmic-pink);
    background: transparent;
    height: 5px;
    border-radius: 2px;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
}
.mini-player-time {
    font-size: 1rem;
    color: #fff;
    min-width: 70px;
    max-width: 90px;
    text-align: right;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
}
.mini-player-volume-label {
    font-size: 1.1rem;
    color: #fff;
    margin-left: 0.7rem;
    margin-right: 0.2rem;
    vertical-align: middle;
}
.mini-player-volume {
    min-width: 70px;
    max-width: 100px;
    accent-color: var(--neon-blue);
    vertical-align: middle;
    margin-left: 0.2rem;
    margin-right: 0.7rem;
}
.mini-player audio {
    display: none;
}
.mini-player-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 2.1rem;
    margin-left: 1rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}
.mini-player-close:hover, .mini-player-close:focus {
    opacity: 1;
    outline: none;
}
@media (max-width: 900px) {
    .mini-player {
        min-width: 0;
        max-width: 98vw;
        padding: 0.7rem 0.5rem;
        gap: 1.2rem;
    }
    .mini-player-info {
        max-width: 160px;
        gap: 0.7rem;
    }
    .mini-player-cover {
        width: 44px;
        height: 44px;
        margin-right: 0.5rem;
    }
    .mini-player-meta {
        max-width: 90px;
    }
    .mini-player-progress {
        min-width: 60px;
        max-width: 100%;
    }
}
@media (max-width: 600px) {
    .mini-player {
        flex-direction: column;
        align-items: stretch;
        gap: 0.7rem;
        min-width: 0;
        max-width: 99vw;
        padding: 0.7rem 0.3rem;
    }
    .mini-player-info {
        flex-direction: row;
        gap: 0.7rem;
        max-width: 100vw;
    }
    .mini-player-meta {
        max-width: 80vw;
    }
    .mini-player-progress {
        width: 100%;
        min-width: 0;
        max-width: 100%;
        margin: 0.5rem 0;
    }
    .mini-player-time {
        margin: 0.2rem 0;
    }
}

/* Mini Player Accessibility & Animation Enhancements */
.mini-player {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: #222;
  color: #fff;
  padding: 20px;
  border-radius: 10px;
  z-index: 9999;
  min-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.4);
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 95vw;
  transition: box-shadow 0.3s;
}
.mini-player:focus-within {
  box-shadow: 0 0 0 3px var(--cosmic-pink, #FF006E);
}
.mini-player-row {
  display: flex;
  align-items: center;
  gap: 10px;
}
.mini-player-info {
  margin-top: 8px;
  font-size: 1rem;
  line-height: 1.2;
}
.mini-progress-row {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.mini-control-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  outline: none;
}
.mini-control-btn:focus {
  background: var(--hover-color, #333);
  box-shadow: 0 0 0 2px var(--cosmic-pink, #FF006E);
}
#progressBar {
  flex: 1 1 0%;
  accent-color: var(--cosmic-pink, #FF006E);
  height: 4px;
  border-radius: 2px;
  background: linear-gradient(90deg, var(--neon-blue, #00E0FF), var(--cosmic-pink, #FF006E));
  transition: background 0.3s;
  outline: none;
}
#progressBar:focus {
  box-shadow: 0 0 0 2px var(--cosmic-pink, #FF006E);
}
#progressBar::-webkit-slider-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--cosmic-pink, #FF006E);
  box-shadow: 0 0 8px var(--cosmic-pink, #FF006E);
  transition: box-shadow 0.2s;
  animation: pulse-thumb 1.2s infinite alternate;
}
#progressBar::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--cosmic-pink, #FF006E);
  box-shadow: 0 0 8px var(--cosmic-pink, #FF006E);
  transition: box-shadow 0.2s;
  animation: pulse-thumb 1.2s infinite alternate;
}
#progressBar::-ms-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--cosmic-pink, #FF006E);
  box-shadow: 0 0 8px var(--cosmic-pink, #FF006E);
  transition: box-shadow 0.2s;
  animation: pulse-thumb 1.2s infinite alternate;
}
@keyframes pulse-thumb {
  0% { box-shadow: 0 0 8px var(--cosmic-pink, #FF006E); }
  100% { box-shadow: 0 0 16px var(--cosmic-pink, #FF006E), 0 0 32px var(--neon-blue, #00E0FF, 0.3); }
}
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0,0,0,0) !important;
  border: 0 !important;
}
@media (max-width: 600px) {
  .mini-player {
    min-width: 0;
    width: 98vw;
    left: 1vw;
    right: 1vw;
    padding: 0.7rem 0.5rem;
    gap: 1.2rem;
  }
  .mini-player-info {
    max-width: 160px;
    gap: 0.7rem;
  }
  .mini-player-cover {
    width: 44px;
    height: 44px;
    margin-right: 0.5rem;
  }
  .mini-player-meta {
    max-width: 90px;
  }
  .mini-player-progress {
    min-width: 60px;
    max-width: 100%;
  }
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}

/* --- Mini Player: Modern, Spacious, and Styled --- */
.mini-player {
    position: fixed;
    left: 50%;
    bottom: 32px;
    transform: translateX(-50%);
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    color: #fff;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(56, 12, 97, 0.25);
    display: flex;
    align-items: center;
    gap: 2.2rem;
    padding: 1.2rem 2.8rem 1.2rem 2rem;
    z-index: 2000;
    min-width: 440px;
    max-width: 650px;
    width: auto;
    animation: fadeInUp 0.4s;
}
.mini-player[hidden], .mini-player.hide {
    display: none !important;
}
.mini-player-info {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    min-width: 0;
    max-width: 260px;
    flex: 1 1 220px;
}
.mini-player-cover {
    flex-shrink: 0;
    width: 64px;
    height: 64px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.22);
    margin-right: 1rem;
}
.mini-player-meta {
    min-width: 100px;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-title {
    font-weight: 700;
    font-size: 1.13rem;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0,0,0,0.18);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-artist {
    font-size: 1rem;
    color: #e0e0e0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mini-player-playpause {
    background: rgba(0,0,0,0.22);
    border: none;
    color: #fff;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    font-size: 1.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    outline: none;
    border: 2px solid transparent;
}
.mini-player-playpause:focus {
    border: 2px solid var(--neon-blue);
    background: rgba(0,224,255,0.18);
}
.mini-player-playpause:hover {
    background: rgba(255,255,255,0.22);
    color: var(--cosmic-pink);
}
.mini-player-playpause i {
    pointer-events: none;
    margin: 0 auto;
}
.mini-player-progress {
    min-width: 120px;
    max-width: 180px;
    accent-color: var(--cosmic-pink);
    background: transparent;
    height: 5px;
    border-radius: 2px;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
}
.mini-player-time {
    font-size: 1rem;
    color: #fff;
    min-width: 70px;
    max-width: 90px;
    text-align: right;
    margin-left: 0.7rem;
    margin-right: 0.7rem;
}
.mini-player-volume-label {
    font-size: 1.1rem;
    color: #fff;
    margin-left: 0.7rem;
    margin-right: 0.2rem;
    vertical-align: middle;
}
.mini-player-volume {
    min-width: 70px;
    max-width: 100px;
    accent-color: var(--neon-blue);
    vertical-align: middle;
    margin-left: 0.2rem;
    margin-right: 0.7rem;
}
.mini-player audio {
    display: none;
}
.mini-player-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 2.1rem;
    margin-left: 1rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}
.mini-player-close:hover, .mini-player-close:focus {
    opacity: 1;
    outline: none;
}
@media (max-width: 900px) {
    .mini-player {
        min-width: 0;
        max-width: 98vw;
        padding: 0.7rem 0.5rem;
        gap: 1.2rem;
    }
    .mini-player-info {
        max-width: 160px;
        gap: 0.7rem;
    }
    .mini-player-cover {
        width: 44px;
        height: 44px;
        margin-right: 0.5rem;
    }
    .mini-player-meta {
        max-width: 90px;
    }
    .mini-player-progress {
        min-width: 60px;
        max-width: 100%;
    }
}
@media (max-width: 600px) {
  #miniPlayerTest {
    min-width: 0;
    width: 96vw;
    left: 2vw;
    right: 2vw;
    padding: 10px 6vw 10px 6vw;
    font-size: 0.97rem;
  }
  #miniPlayerTest .mini-row, #miniPlayerTest .mini-progress-row {
    gap: 7px;
  }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-navigation {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-tabs {
        justify-content: center;
        flex-wrap: wrap;
    }

    .chart-filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-actions {
        gap: 0.75rem;
    }
}

@media (max-width: 1024px) {
    .featured-chart-hero {
        height: 350px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-artist {
        font-size: 1.1rem;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .chart-card-image {
        width: 100px;
        height: 100px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .charts-title-gradient {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .chart-navigation {
        padding: 1rem;
    }

    .chart-tabs {
        gap: 0.25rem;
    }

    .chart-tab {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .chart-filter {
        min-width: 120px;
        padding: 0.6rem 0.75rem;
    }

    .featured-chart-hero {
        height: 300px;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .hero-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .hero-play-btn,
    .hero-action-btn {
        width: 100%;
        justify-content: center;
    }

    .chart-section {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chart-item {
        padding: 1rem;
        gap: 1rem;
    }

    .chart-item-image {
        width: 50px;
        height: 50px;
    }

    .chart-position {
        font-size: 1.2rem;
        min-width: 30px;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-card-image {
        width: 80px;
        height: 80px;
    }

    .chart-card-position {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .charts-title-gradient {
        font-size: 2rem;
    }

    .chart-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-tab {
        justify-content: center;
        padding: 1rem;
    }

    .chart-filters {
        flex-direction: column;
        gap: 0.75rem;
    }

    .chart-filter {
        width: 100%;
    }

    .featured-chart-hero {
        height: 250px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-artist {
        font-size: 1rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-item-stats {
        align-items: center;
    }

    .chart-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-card-title {
        font-size: 1rem;
    }

    .chart-card-subtitle {
        font-size: 0.8rem;
    }
}

/* Focus styles for accessibility */
.chart-tab:focus,
.chart-filter:focus,
.hero-play-btn:focus,
.hero-action-btn:focus,
.view-all-btn:focus,
.chart-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .chart-section,
    .chart-item,
    .chart-card,
    .stat-card {
        border: 2px solid var(--text-primary);
    }

    .chart-tab.active,
    .hero-play-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .chart-filter {
        border: 2px solid var(--text-primary);
    }
}

/* Print styles */
@media print {
    .hero-actions,
    .chart-item-actions,
    .chart-action-btn {
        display: none;
    }

    .featured-chart-hero {
        background: white;
        color: black;
    }

    .chart-section {
        border: 1px solid #000;
        background: white;
    }
}