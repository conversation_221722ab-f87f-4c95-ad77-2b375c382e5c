<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore Music - BansheeBlast</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/explore.css">
</head>

<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>

            <!-- Hamburger <PERSON><PERSON> But<PERSON> -->
            <button type="button" class="hamburger" id="hamburger" aria-label="Toggle navigation menu" aria-expanded="false">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <ul class="menu" id="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html" aria-current="page">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu"
                    aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
    </header>

    <main id="main-content" aria-label="Explore Music Main Content">
        <section class="explore-hero" aria-label="Explore Hero Section">
            <div class="hero-background">
                <div class="hero-particles"></div>
                <div class="hero-gradient-overlay"></div>
            </div>
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-compass"></i>
                    Music Discovery
                </div>
                <h1 class="explore-title-gradient">Explore Music</h1>
                <p class="hero-subtitle">Discover new artists, genres, and tracks tailored to your taste. Dive into a world of endless musical possibilities.</p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">50M+</span>
                        <span class="stat-label">Songs</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">2M+</span>
                        <span class="stat-label">Artists</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">500K+</span>
                        <span class="stat-label">Albums</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="#discover" class="cta-button primary">
                        <i class="fas fa-play"></i>
                        Start Discovering
                    </a>
                    <a href="trendingcharts.html" class="cta-button secondary">
                        <i class="fas fa-chart-line"></i>
                        View Charts
                    </a>
                </div>
            </div>
        </section>

        <section id="discover" class="discover-section" aria-label="Music Discovery and Search">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-search"></i>
                    Discover Music
                </h2>
                <p class="section-description">Search through millions of tracks, artists, and albums</p>
            </div>

            <div class="search-container">
                <form class="search-form" role="search" aria-label="Music Search">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon" aria-hidden="true"></i>
                        <input type="search" id="searchInput" class="search-bar"
                               placeholder="Search for artists, songs, albums, or genres..."
                               aria-label="Search for music" minlength="2" autocomplete="off">
                        <button type="button" class="search-clear hidden" id="searchClear" aria-label="Clear search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <button type="submit" class="search-button">
                        <i class="fas fa-search"></i>
                        Search
                    </button>
                </form>

                <div class="quick-search-tags">
                    <span class="tags-label">Quick Search:</span>
                    <button type="button" class="search-tag" data-query="trending">Trending</button>
                    <button type="button" class="search-tag" data-query="new releases">New Releases</button>
                    <button type="button" class="search-tag" data-query="electronic">Electronic</button>
                    <button type="button" class="search-tag" data-query="indie">Indie</button>
                    <button type="button" class="search-tag" data-query="jazz">Jazz</button>
                    <button type="button" class="search-tag" data-query="rock">Rock</button>
                </div>
            </div>

            <div class="filters-container" aria-label="Music Filters">
                <select class="filter-select" aria-label="Filter by genre">
                    <option value="">Genre</option>
                    <option value="pop">Pop</option>
                    <option value="rock">Rock</option>
                    <option value="hiphop">Hip-Hop</option>
                    <option value="rnb">R&B</option>
                    <option value="jazz">Jazz</option>
                    <option value="electronic">Electronic</option>
                    <option value="classical">Classical</option>
                    <option value="indie">Indie</option>
                    <option value="metal">Metal</option>
                    <option value="blues">Blues</option>
                    <option value="country">Country</option>
                    <option value="folk">Folk</option>
                    <option value="reggae">Reggae</option>
                    <option value="latin">Latin</option>
                    <option value="soul">Soul</option>
                    <option value="funk">Funk</option>
                    <option value="punk">Punk</option>
                    <option value="ambient">Ambient</option>
                    <option value="edm">EDM</option>
                    <option value="trap">Trap</option>
                    <option value="house">House</option>
                    <option value="techno">Techno</option>
                    <option value="alternative">Alternative</option>
                    <option value="world">World</option>
                    <option value="instrumental">Instrumental</option>
                    <option value="soundtrack">Soundtrack</option>
                    <option value="gospel">Gospel</option>
                </select>
                <select class="filter-select" aria-label="Filter by popularity">
                    <option value="">Popularity</option>
                    <option value="most-popular">Most Popular</option>
                    <option value="trending">Trending</option>
                    <option value="new-releases">New Releases</option>
                </select>
                <select class="filter-select" aria-label="Filter by release date">
                    <option value="">Release Date</option>
                    <option value="today">Today</option>
                    <option value="this-week">This Week</option>
                    <option value="last-week">Last Week</option>
                    <option value="this-month">This Month</option>
                    <option value="last-month">Last Month</option>
                    <option value="last-3-months">Last 3 Months</option>
                    <option value="last-6-months">Last 6 Months</option>
                    <option value="this-year">This Year</option>
                    <option value="last-year">Last Year</option>
                    <option value="last-2-years">Last 2 Years</option>
                    <option value="last-5-years">Last 5 Years</option>
                    <option value="2010s">2010s</option>
                    <option value="2000s">2000s</option>
                    <option value="1990s">1990s</option>
                    <option value="1980s">1980s</option>
                    <option value="1970s">1970s</option>
                    <option value="older">Older</option>
                </select>
            </div>
            <div class="results-count" aria-live="polite"></div>
            <div class="results-container" id="resultsContainer">
                <span class="empty-results">Start typing to search for music.</span>
            </div>
        </section>

        <!-- Genre Exploration Section -->
        <section class="genre-section" aria-label="Music Genres">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-palette"></i>
                    Explore by Genre
                </h2>
                <p class="section-description">Dive into different musical worlds and discover your new favorite sound</p>
            </div>
            <div class="genre-grid">
                <div class="genre-card" data-genre="pop" tabindex="0">
                    <div class="genre-background">
                        <div class="genre-pattern pop-pattern"></div>
                    </div>
                    <div class="genre-content">
                        <div class="genre-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3>Pop</h3>
                        <p>Catchy melodies and mainstream appeal</p>
                        <div class="genre-stats">
                            <span><i class="fas fa-music"></i> 2.1M tracks</span>
                        </div>
                    </div>
                </div>

                <div class="genre-card" data-genre="rock" tabindex="0">
                    <div class="genre-background">
                        <div class="genre-pattern rock-pattern"></div>
                    </div>
                    <div class="genre-content">
                        <div class="genre-icon">
                            <i class="fas fa-guitar"></i>
                        </div>
                        <h3>Rock</h3>
                        <p>Powerful rhythms and electric energy</p>
                        <div class="genre-stats">
                            <span><i class="fas fa-music"></i> 1.8M tracks</span>
                        </div>
                    </div>
                </div>

                <div class="genre-card" data-genre="electronic" tabindex="0">
                    <div class="genre-background">
                        <div class="genre-pattern electronic-pattern"></div>
                    </div>
                    <div class="genre-content">
                        <div class="genre-icon">
                            <i class="fas fa-headphones"></i>
                        </div>
                        <h3>Electronic</h3>
                        <p>Digital beats and synthesized sounds</p>
                        <div class="genre-stats">
                            <span><i class="fas fa-music"></i> 1.5M tracks</span>
                        </div>
                    </div>
                </div>

                <div class="genre-card" data-genre="hip-hop" tabindex="0">
                    <div class="genre-background">
                        <div class="genre-pattern hiphop-pattern"></div>
                    </div>
                    <div class="genre-content">
                        <div class="genre-icon">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <h3>Hip-Hop</h3>
                        <p>Rhythmic speech and urban culture</p>
                        <div class="genre-stats">
                            <span><i class="fas fa-music"></i> 1.3M tracks</span>
                        </div>
                    </div>
                </div>

                <div class="genre-card" data-genre="jazz" tabindex="0">
                    <div class="genre-background">
                        <div class="genre-pattern jazz-pattern"></div>
                    </div>
                    <div class="genre-content">
                        <div class="genre-icon">
                            <i class="fas fa-music"></i>
                        </div>
                        <h3>Jazz</h3>
                        <p>Improvisation and sophisticated harmonies</p>
                        <div class="genre-stats">
                            <span><i class="fas fa-music"></i> 800K tracks</span>
                        </div>
                    </div>
                </div>

                <div class="genre-card" data-genre="indie" tabindex="0">
                    <div class="genre-background">
                        <div class="genre-pattern indie-pattern"></div>
                    </div>
                    <div class="genre-content">
                        <div class="genre-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3>Indie</h3>
                        <p>Independent artists and unique sounds</p>
                        <div class="genre-stats">
                            <span><i class="fas fa-music"></i> 950K tracks</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Artists Section -->
        <article class="section featured-artists" aria-labelledby="featured-artists-title">
            <div class="section-header">
                <h2 id="featured-artists-title">Featured Artists</h2>
                <p class="section-description">Discover talented artists making waves</p>
            </div>
            <div class="carousel-container" role="region" aria-label="Featured Artists Carousel" tabindex="0">
                <button type="button" class="carousel-button prev" aria-label="View previous artists"></button>
                <div class="carousel-track">
                    <!-- Artist Card 1 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-01.png" alt="Featured Artist: Artist Alpha" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Artist Alpha">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist Alpha</h3>
                                    <p>Genre: Indie Pop</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 2 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png" alt="Featured Artist: Beta Beats" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Beta Beats">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Beta Beats</h3>
                                    <p>Genre: Lo-fi Hip Hop</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 3 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png" alt="Featured Artist: Gamma Grooves" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Gamma Grooves">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Gamma Grooves</h3>
                                    <p>Genre: Funk, Soul</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 4 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-04.png" alt="Featured Artist: Delta Waves" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Delta Waves">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Delta Waves</h3>
                                    <p>Genre: Ambient Electronic</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 5 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-05.png" alt="Featured Artist: Epsilon Echoes" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Epsilon Echoes">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Epsilon Echoes</h3>
                                    <p>Genre: Synthwave</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 6 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-06.png" alt="Featured Artist: Zeta Flow" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Zeta Flow">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Zeta Flow</h3>
                                    <p>Genre: Chillhop</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="carousel-button next" aria-label="View next artists"></button>
            </div>
        </article>
    </main>

    <div id="aria-live-region" aria-live="polite" class="sr-only"></div>

    <!-- Shared utilities -->
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script src="js/services/deezer-service.js"></script>
    <script src="js/explore.js"></script>
</body>

</html>